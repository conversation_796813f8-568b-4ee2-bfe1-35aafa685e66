{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue", "mtime": 1750148832803}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0IjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZXZlcnkiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc29tZSI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudG8tc3RyaW5nIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3BsaXQiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovd29ya3NwYWNlL3NtcC9ub2RlX21vZHVsZXMvQHZ1ZS9iYWJlbC1wcmVzZXQtYXBwL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyIjsKaW1wb3J0ICJyZWdlbmVyYXRvci1ydW50aW1lL3J1bnRpbWUiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovd29ya3NwYWNlL3NtcC9ub2RlX21vZHVsZXMvQHZ1ZS9iYWJlbC1wcmVzZXQtYXBwL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KaW1wb3J0IHsgZ2V0RmlyZXdhbGxEZXZpY2VMaXN0IGFzIF9nZXRGaXJld2FsbERldmljZUxpc3QsIGRlbGV0ZUZpcmV3YWxsRGV2aWNlLCBiYXRjaERlbGV0ZUZpcmV3YWxsRGV2aWNlLCBkZXZpY2VQaW5nLCBnZXRUb3BvRGF0YSwgc2V0VG9wb0RhdGEgfSBmcm9tICdAL2FwaS9maXJld2FsbC9kZXZpY2VNYW5hZ2VtZW50JzsKaW1wb3J0IEFkZERldmljZU1vZGFsIGZyb20gJy4vY29tcG9uZW50cy9BZGREZXZpY2VNb2RhbC52dWUnOwppbXBvcnQgVXNlck1hbmFnZU1vZGFsIGZyb20gJy4vY29tcG9uZW50cy9Vc2VyTWFuYWdlTW9kYWwudnVlJzsgLy8g55Sf5oiQ5ZSv5LiASUTnmoTlt6Xlhbflh73mlbAKCmZ1bmN0aW9uIGd1aWQoKSB7CiAgcmV0dXJuICd4eHh4eHh4eC14eHh4LTR4eHgteXh4eC14eHh4eHh4eHh4eHgnLnJlcGxhY2UoL1t4eV0vZywgZnVuY3Rpb24gKGMpIHsKICAgIHZhciByID0gTWF0aC5yYW5kb20oKSAqIDE2IHwgMDsKICAgIHZhciB2ID0gYyA9PT0gJ3gnID8gciA6IHIgJiAweDMgfCAweDg7CiAgICByZXR1cm4gdi50b1N0cmluZygxNik7CiAgfSk7Cn0KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGV2aWNlTGlzdCcsCiAgY29tcG9uZW50czogewogICAgQWRkRGV2aWNlTW9kYWw6IEFkZERldmljZU1vZGFsLAogICAgVXNlck1hbmFnZU1vZGFsOiBVc2VyTWFuYWdlTW9kYWwKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVUYWI6ICcwJywKICAgICAgaXNTaG93OiBmYWxzZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHF1ZXJ5SW5wdXQ6IHsKICAgICAgICBmaXJlTmFtZTogJycsCiAgICAgICAgb3JpZ2luSXA6ICcnLAogICAgICAgIG9ubGluU3RhdHVzOiAnJwogICAgICB9LAogICAgICB0YWJsZURhdGE6IFtdLAogICAgICBzZWxlY3RlZFJvd3M6IFtdLAogICAgICBwYWdpbmF0aW9uOiB7CiAgICAgICAgdG90YWw6IDAsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwKICAgICAgYWRkTW9kYWxWaXNpYmxlOiBmYWxzZSwKICAgICAgdXNlck1vZGFsVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnREYXRhOiBudWxsLAogICAgICBwYW5lczogW10sCiAgICAgIC8vIOWKqOaAgeagh+etvumhtQogICAgICB0aW1lcjogbnVsbCwKICAgICAgLy8g5a6a5pe25ZmoCiAgICAgIHRvcG9EYXRhOiB7fSAvLyDlrZjlgqjmi5PmiZHmlbDmja4KCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7CiAgICB0aGlzLmdldFRvcG9EYXRhRnJvbVNlcnZlcigpOwogICAgdGhpcy5zdGFydFRpbWVyKCk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgdGhpcy5jbGVhclRpbWVyKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICB0b2dnbGVTaG93OiBmdW5jdGlvbiB0b2dnbGVTaG93KCkgewogICAgICB0aGlzLmlzU2hvdyA9ICF0aGlzLmlzU2hvdzsKICAgIH0sCiAgICBzdGFydFRpbWVyOiBmdW5jdGlvbiBzdGFydFRpbWVyKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgLy8g5q+PMzDnp5LliLfmlrDkuIDmrKHorr7lpIfliJfooagKICAgICAgdGhpcy50aW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKTsKICAgICAgfSwgMzAwMDApOwogICAgfSwKICAgIGNsZWFyVGltZXI6IGZ1bmN0aW9uIGNsZWFyVGltZXIoKSB7CiAgICAgIGlmICh0aGlzLnRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsKICAgICAgICB0aGlzLnRpbWVyID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIGdldEZpcmV3YWxsRGV2aWNlTGlzdDogZnVuY3Rpb24gZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHBheWxvYWQsIHJlczsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczIubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgICBwYXlsb2FkID0gewogICAgICAgICAgICAgICAgICBfbGltaXQ6IF90aGlzMi5wYWdpbmF0aW9uLnBhZ2VTaXplLAogICAgICAgICAgICAgICAgICBfcGFnZTogX3RoaXMyLnBhZ2luYXRpb24uY3VycmVudFBhZ2UsCiAgICAgICAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOiBfdGhpczIuYnVpbGRRdWVyeVBhcmFtcygpLAogICAgICAgICAgICAgICAgICB0eXBlOiAxCiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDI7CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNTsKICAgICAgICAgICAgICAgIHJldHVybiBfZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KHBheWxvYWQpOwoKICAgICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dC5zZW50OwoKICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgICAgICAgICBfdGhpczIudGFibGVEYXRhID0gcmVzLmRhdGEuaXRlbXMgfHwgW107CiAgICAgICAgICAgICAgICAgIF90aGlzMi5wYWdpbmF0aW9uLnRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMDsKICAgICAgICAgICAgICAgICAgX3RoaXMyLnNlbGVjdGVkUm93cyA9IFtdOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTI7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDk7CiAgICAgICAgICAgICAgICBfY29udGV4dC50MCA9IF9jb250ZXh0WyJjYXRjaCJdKDIpOwoKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6+5aSH5YiX6KGo5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gMTI7CiAgICAgICAgICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmZpbmlzaCgxMik7CgogICAgICAgICAgICAgIGNhc2UgMTU6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzIsIDksIDEyLCAxNV1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgYnVpbGRRdWVyeVBhcmFtczogZnVuY3Rpb24gYnVpbGRRdWVyeVBhcmFtcygpIHsKICAgICAgdmFyIHBhcmFtcyA9IHt9OwogICAgICBpZiAodGhpcy5xdWVyeUlucHV0LmZpcmVOYW1lKSBwYXJhbXMuZmlyZU5hbWUgPSB0aGlzLnF1ZXJ5SW5wdXQuZmlyZU5hbWU7CiAgICAgIGlmICh0aGlzLnF1ZXJ5SW5wdXQub3JpZ2luSXApIHBhcmFtcy5vcmlnaW5JcCA9IHRoaXMucXVlcnlJbnB1dC5vcmlnaW5JcDsKICAgICAgaWYgKHRoaXMucXVlcnlJbnB1dC5vbmxpblN0YXR1cyAhPT0gJycpIHBhcmFtcy5vbmxpblN0YXR1cyA9IHRoaXMucXVlcnlJbnB1dC5vbmxpblN0YXR1czsKICAgICAgcmV0dXJuIHBhcmFtczsKICAgIH0sCiAgICBoYW5kbGVUYWJDbGljazogZnVuY3Rpb24gaGFuZGxlVGFiQ2xpY2sodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiLm5hbWU7CgogICAgICBpZiAodGFiLm5hbWUgIT09ICcwJykgey8vIOWkhOeQhuWKqOaAgeagh+etvumhteeCueWHuwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSAxOwogICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpOwogICAgfSwKICAgIGhhbmRsZVJlc2V0OiBmdW5jdGlvbiBoYW5kbGVSZXNldCgpIHsKICAgICAgdGhpcy5xdWVyeUlucHV0ID0gewogICAgICAgIGZpcmVOYW1lOiAnJywKICAgICAgICBvcmlnaW5JcDogJycsCiAgICAgICAgb25saW5TdGF0dXM6ICcnCiAgICAgIH07CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBoYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5jdXJyZW50RGF0YSA9IG51bGw7CiAgICAgIHRoaXMuYWRkTW9kYWxWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVFZGl0OiBmdW5jdGlvbiBoYW5kbGVFZGl0KHJlY29yZCkgewogICAgICB0aGlzLmN1cnJlbnREYXRhID0gcmVjb3JkOwogICAgICB0aGlzLmFkZE1vZGFsVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyZWNvcmQpIHsKICAgICAgLy8g5omT5byA6K6+5aSH566h55CG6aG16Z2iCiAgICAgIHZhciBwYW5lID0gewogICAgICAgIHRpdGxlOiByZWNvcmQuZmlyZU5hbWUsCiAgICAgICAgbmFtZTogcmVjb3JkLmlwLAogICAgICAgIGNvbnRlbnQ6ICJodHRwczovLyIuY29uY2F0KHJlY29yZC5vcmlnaW5JcCwgIjoiKS5jb25jYXQocmVjb3JkLnBvcnQpCiAgICAgIH07IC8vIOajgOafpeaYr+WQpuW3sue7j+aJk+W8gAoKICAgICAgdmFyIGV4aXN0aW5nUGFuZSA9IHRoaXMucGFuZXMuZmluZChmdW5jdGlvbiAocCkgewogICAgICAgIHJldHVybiBwLm5hbWUgPT09IHBhbmUubmFtZTsKICAgICAgfSk7CgogICAgICBpZiAoIWV4aXN0aW5nUGFuZSkgewogICAgICAgIHRoaXMucGFuZXMucHVzaChwYW5lKTsKICAgICAgfQoKICAgICAgdGhpcy5hY3RpdmVUYWIgPSBwYW5lLm5hbWU7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocmVjb3JkKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6K+l6K6+5aSH5ZCXP+WIoOmZpOWQjuS4jeWPr+aBouWkjScsICfliKDpmaQnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7orqQnLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCAvKiNfX1BVUkVfXyovX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICB2YXIgcmVzOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDA7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gZGVsZXRlRmlyZXdhbGxEZXZpY2UoewogICAgICAgICAgICAgICAgICBkZXZpY2VfaWQ6IHJlY29yZC5pZAogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0Mi5zZW50OwoKICAgICAgICAgICAgICAgIGlmIChyZXMucmV0Y29kZSA9PT0gMCkgewogICAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJyk7CgogICAgICAgICAgICAgICAgICBfdGhpczMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7IC8vIOS7juaLk+aJkeS4reWIoOmZpOiuvuWkh+iKgueCuQoKCiAgICAgICAgICAgICAgICAgIF90aGlzMy5kZWxEZXZpY2VOb2RlKFtyZWNvcmQub3JpZ2luSXBdKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEwOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gNzsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi50MCA9IF9jb250ZXh0MlsiY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGhhbmRsZUJhdGNoRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVCYXRjaERlbGV0ZSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6Iez5bCR6YCJ5Lit5LiA5p2h5pWw5o2uJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTpgInkuK3orr7lpIflkJc/5Yig6Zmk5ZCO5LiN5Y+v5oGi5aSNJywgJ+WIoOmZpCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruiupCcsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oIC8qI19fUFVSRV9fKi9fYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciBpZHMsIHJlcywgZGV2aWNlSXBzOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDMucHJldiA9IDA7CiAgICAgICAgICAgICAgICBpZHMgPSBfdGhpczQuc2VsZWN0ZWRSb3dzLm1hcChmdW5jdGlvbiAocm93KSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiByb3cuaWQ7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gNDsKICAgICAgICAgICAgICAgIHJldHVybiBiYXRjaERlbGV0ZUZpcmV3YWxsRGV2aWNlKHsKICAgICAgICAgICAgICAgICAgZGV2aWNlX2lkczogaWRzCiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQzLnNlbnQ7CgogICAgICAgICAgICAgICAgaWYgKHJlcy5yZXRjb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKCiAgICAgICAgICAgICAgICAgIF90aGlzNC5nZXRGaXJld2FsbERldmljZUxpc3QoKTsgLy8g5LuO5ouT5omR5Lit5om56YeP5Yig6Zmk6K6+5aSH6IqC54K5CgoKICAgICAgICAgICAgICAgICAgZGV2aWNlSXBzID0gX3RoaXM0LnNlbGVjdGVkUm93cy5tYXAoZnVuY3Rpb24gKHJvdykgewogICAgICAgICAgICAgICAgICAgIHJldHVybiByb3cub3JpZ2luSXA7CiAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgX3RoaXM0LmRlbERldmljZU5vZGUoZGV2aWNlSXBzKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDExOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gODsKICAgICAgICAgICAgICAgIF9jb250ZXh0My50MCA9IF9jb250ZXh0M1siY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzLCBudWxsLCBbWzAsIDhdXSk7CiAgICAgIH0pKSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGhhbmRsZVBpbmc6IGZ1bmN0aW9uIGhhbmRsZVBpbmcocmVjb3JkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIHJlczsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDQucHJldiA9IF9jb250ZXh0NC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQ0LnByZXYgPSAwOwoKICAgICAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5pbmZvKCfmraPlnKjmtYvor5Xov57mjqUuLi4nKTsKCiAgICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDQ7CiAgICAgICAgICAgICAgICByZXR1cm4gZGV2aWNlUGluZyh7CiAgICAgICAgICAgICAgICAgIGlwOiByZWNvcmQub3JpZ2luSXAKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDQuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+iuvuWkh+i/nuaOpeato+W4uCcpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmVycm9yKCforr7lpIfov57mjqXlpLHotKUnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDExOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5wcmV2ID0gODsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC50MCA9IF9jb250ZXh0NFsiY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczUuJG1lc3NhZ2UuZXJyb3IoJ+i/nuaOpea1i+ivleWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU0LCBudWxsLCBbWzAsIDhdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZVVzZXJNYW5hZ2U6IGZ1bmN0aW9uIGhhbmRsZVVzZXJNYW5hZ2UocmVjb3JkKSB7CiAgICAgIHRoaXMuY3VycmVudERhdGEgPSByZWNvcmQ7CiAgICAgIHRoaXMudXNlck1vZGFsVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlQWRkU3VibWl0OiBmdW5jdGlvbiBoYW5kbGVBZGRTdWJtaXQoZGV2aWNlRGF0YSkgewogICAgICB0aGlzLmFkZE1vZGFsVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpOyAvLyDlpoLmnpzmnInorr7lpIfmlbDmja7vvIzmt7vliqDliLDmi5PmiZHkuK0KCiAgICAgIGlmIChkZXZpY2VEYXRhICYmIGRldmljZURhdGEuaXApIHsKICAgICAgICB0aGlzLmFkZERldmljZU5vZGUoZGV2aWNlRGF0YSk7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVVc2VyU3VibWl0OiBmdW5jdGlvbiBoYW5kbGVVc2VyU3VibWl0KCkgewogICAgICB0aGlzLnVzZXJNb2RhbFZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBzZWxlY3Rpb247CiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5wYWdlU2l6ZSA9IHNpemU7CiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlUGFnZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlUGFnZUNoYW5nZShwYWdlKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5jdXJyZW50UGFnZSA9IHBhZ2U7CiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7CiAgICB9LAogICAgLy8gPT09PT09PT09PT09PT09PT09PT0g5ouT5omR55u45YWz5pa55rOVID09PT09PT09PT09PT09PT09PT09CgogICAgLyoqCiAgICAgKiDojrflj5bmi5PmiZHmlbDmja4KICAgICAqLwogICAgZ2V0VG9wb0RhdGFGcm9tU2VydmVyOiBmdW5jdGlvbiBnZXRUb3BvRGF0YUZyb21TZXJ2ZXIoY2FsbGJhY2spIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CgogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICB2YXIgcmVzOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlNSQoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0NS5wcmV2ID0gX2NvbnRleHQ1Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDA7CiAgICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gZ2V0VG9wb0RhdGEoKTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ1LnNlbnQ7CgogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNi50b3BvRGF0YSA9IHJlcy5kYXRhIHx8IHsKICAgICAgICAgICAgICAgICAgICBub2RlczogW10sCiAgICAgICAgICAgICAgICAgICAgZWRnZXM6IFtdCiAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgIGlmIChjYWxsYmFjaykgY2FsbGJhY2soKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAxMDsKICAgICAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDc7CiAgICAgICAgICAgICAgICBfY29udGV4dDUudDAgPSBfY29udGV4dDVbImNhdGNoIl0oMCk7CgogICAgICAgICAgICAgICAgX3RoaXM2LiRtZXNzYWdlLmVycm9yKCfojrflj5bmi5PmiZHmlbDmja7lpLHotKUnKTsKCiAgICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSwgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDkv53lrZjmi5PmiZHmlbDmja4KICAgICAqIEBwYXJhbSB0b3BvRGF0YQogICAgICovCiAgICBzYXZlVG9wb0RhdGFUb1NlcnZlcjogZnVuY3Rpb24gc2F2ZVRvcG9EYXRhVG9TZXJ2ZXIodG9wb0RhdGEpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CgogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlNigpIHsKICAgICAgICB2YXIgcmVzOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlNiQoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Ni5wcmV2ID0gX2NvbnRleHQ2Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDYucHJldiA9IDA7CiAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gc2V0VG9wb0RhdGEoewogICAgICAgICAgICAgICAgICB0b3BvbG9neV90ZXh0OiB0b3BvRGF0YQogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0Ni5zZW50OwoKICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgICAgICAgICBfdGhpczcuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgICAgX2NvbnRleHQ2LnByZXYgPSA3OwogICAgICAgICAgICAgICAgX2NvbnRleHQ2LnQwID0gX2NvbnRleHQ2WyJjYXRjaCJdKDApOwoKICAgICAgICAgICAgICAgIF90aGlzNy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5ouT5omR5pWw5o2u5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTYsIG51bGwsIFtbMCwgN11dKTsKICAgICAgfSkpKCk7CiAgICB9LAoKICAgIC8qKgogICAgICog55Sf5oiQ6K6+5aSH6IqC54K5CiAgICAgKi8KICAgIGdlbmVyYXRlTm9kZTogZnVuY3Rpb24gZ2VuZXJhdGVOb2RlKG9wdGlvbnMpIHsKICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoewogICAgICAgIGlkOiBndWlkKCksCiAgICAgICAgdHlwZTogJ25vZGUnLAogICAgICAgIHNpemU6ICc1MCcsCiAgICAgICAgc2hhcGU6ICdrb25pLWN1c3RvbS1ub2RlJywKICAgICAgICBjb2xvcjogJyM2OUMwRkYnLAogICAgICAgIGxhYmVsT2Zmc2V0WTogMzgKICAgICAgfSwgb3B0aW9ucyk7CiAgICB9LAoKICAgIC8qKgogICAgICog5re75Yqg6K6+5aSH6IqC54K55Yqf6IO9CiAgICAgKiBAcGFyYW0gdmFsdWVzCiAgICAgKi8KICAgIGFkZERldmljZU5vZGU6IGZ1bmN0aW9uIGFkZERldmljZU5vZGUodmFsdWVzKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwoKICAgICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoZnVuY3Rpb24gKCkgewogICAgICAgIHZhciB0b3BvRGF0YSA9IF9vYmplY3RTcHJlYWQoe30sIF90aGlzOC50b3BvRGF0YSk7IC8vIOehruS/nSBub2RlcyDlkowgZWRnZXMg5pWw57uE5a2Y5ZyoCgoKICAgICAgICBpZiAoIXRvcG9EYXRhLm5vZGVzKSB0b3BvRGF0YS5ub2RlcyA9IFtdOwogICAgICAgIGlmICghdG9wb0RhdGEuZWRnZXMpIHRvcG9EYXRhLmVkZ2VzID0gW107IC8vIDEuIOWIpOaWrei3r+eUseWZqOaYr+WQpuWtmOWcqO+8jOS4jeWtmOWcqOeUn+aIkOi3r+eUseWZqAoKICAgICAgICB2YXIgaXAgPSB2YWx1ZXMuaXA7CiAgICAgICAgdmFyIGlwbGlzdCA9IGlwLnNwbGl0KCcuJyk7CiAgICAgICAgaXBsaXN0LnBvcCgpOwogICAgICAgIHZhciByb3V0ZXJJcCA9IGlwbGlzdC5qb2luKCcuJykgKyAnLjAnOwogICAgICAgIHZhciBpc0V4aXN0ID0gdG9wb0RhdGEubm9kZXMubGVuZ3RoID4gMCAmJiB0b3BvRGF0YS5ub2Rlcy5zb21lKGZ1bmN0aW9uICh2YWwpIHsKICAgICAgICAgIHJldHVybiB2YWwuZGV2aWNlX2lwID09PSByb3V0ZXJJcDsKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFpc0V4aXN0KSB7CiAgICAgICAgICB2YXIgbmV3Tm9kZSA9IF90aGlzOC5nZW5lcmF0ZU5vZGUoewogICAgICAgICAgICBkZXZpY2VfaXA6IHJvdXRlcklwLAogICAgICAgICAgICBjYXRlZ29yeTogMywKICAgICAgICAgICAgbGFiZWw6ICfot6/nlLHlmagnCiAgICAgICAgICB9KTsKCiAgICAgICAgICB0b3BvRGF0YS5ub2Rlcy5wdXNoKG5ld05vZGUpOwogICAgICAgIH0gLy8gMi4g5Yik5pat6K6+5aSH5piv5ZCm5a2Y5Zyo77yM5LiN5a2Y5Zyo5bCx55Sf5oiQ6K6+5aSH6IqC54K55ZKM6L+e57q/6L65CgoKICAgICAgICB2YXIgaXNEZXZFeGlzdCA9IHRvcG9EYXRhLm5vZGVzLmxlbmd0aCA+IDAgJiYgdG9wb0RhdGEubm9kZXMuc29tZShmdW5jdGlvbiAodmFsKSB7CiAgICAgICAgICByZXR1cm4gdmFsLmRldmljZV9pcCA9PT0gdmFsdWVzLmlwOwogICAgICAgIH0pOwoKICAgICAgICBpZiAoIWlzRGV2RXhpc3QpIHsKICAgICAgICAgIHZhciBfbmV3Tm9kZSA9IF90aGlzOC5nZW5lcmF0ZU5vZGUoewogICAgICAgICAgICBsYWJlbDogJ+mYsueBq+WimScsCiAgICAgICAgICAgIGNhdGVnb3J5OiAxLAogICAgICAgICAgICBkZXZpY2VfaXA6IHZhbHVlcy5pcAogICAgICAgICAgfSk7CgogICAgICAgICAgdG9wb0RhdGEubm9kZXMucHVzaChfbmV3Tm9kZSk7CiAgICAgICAgICB2YXIgc2VydmVyRGF0YSA9IHRvcG9EYXRhLm5vZGVzLmZpbmQoZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICByZXR1cm4gdmFsLmRldmljZV9pcCA9PT0gcm91dGVySXA7CiAgICAgICAgICB9KTsKCiAgICAgICAgICBpZiAoc2VydmVyRGF0YSkgewogICAgICAgICAgICB0b3BvRGF0YS5lZGdlcy5wdXNoKHsKICAgICAgICAgICAgICBpZDogZ3VpZCgpLAogICAgICAgICAgICAgIHNvdXJjZTogc2VydmVyRGF0YS5pZCwKICAgICAgICAgICAgICB0YXJnZXQ6IF9uZXdOb2RlLmlkCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgX3RoaXM4LnNhdmVUb3BvRGF0YVRvU2VydmVyKHRvcG9EYXRhKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKgogICAgICog5Yig6Zmk6K6+5aSH6IqC54K55Yqf6IO9CiAgICAgKiBAcGFyYW0gc2VsZWN0ZWREZXZMaXN0CiAgICAgKi8KICAgIGRlbERldmljZU5vZGU6IGZ1bmN0aW9uIGRlbERldmljZU5vZGUoc2VsZWN0ZWREZXZMaXN0KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwoKICAgICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoZnVuY3Rpb24gKCkgewogICAgICAgIHZhciB0b3BvRGF0YSA9IF9vYmplY3RTcHJlYWQoe30sIF90aGlzOS50b3BvRGF0YSk7IC8vIOehruS/nSBub2RlcyDlkowgZWRnZXMg5pWw57uE5a2Y5ZyoCgoKICAgICAgICBpZiAoIXRvcG9EYXRhLm5vZGVzKSB0b3BvRGF0YS5ub2RlcyA9IFtdOwogICAgICAgIGlmICghdG9wb0RhdGEuZWRnZXMpIHRvcG9EYXRhLmVkZ2VzID0gW107IC8vIDEuIOWIpOaWreiuvuWkh+aYr+WQpuWtmOWcqO+8jOWtmOWcqOWwseWIoOmZpOiKgueCueWSjOi/nue6v+i+uQoKICAgICAgICBzZWxlY3RlZERldkxpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHJlc3VsdCA9IHRvcG9EYXRhLm5vZGVzLmZpbmQoZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICByZXR1cm4gdmFsLmRldmljZV9pcCA9PT0gaXRlbTsKICAgICAgICAgIH0pOwoKICAgICAgICAgIGlmIChyZXN1bHQpIHsKICAgICAgICAgICAgdG9wb0RhdGEubm9kZXMgPSB0b3BvRGF0YS5ub2Rlcy5maWx0ZXIoZnVuY3Rpb24gKG5vZGUpIHsKICAgICAgICAgICAgICByZXR1cm4gbm9kZS5pZCAhPT0gcmVzdWx0LmlkOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdG9wb0RhdGEuZWRnZXMgPSB0b3BvRGF0YS5lZGdlcy5maWx0ZXIoZnVuY3Rpb24gKGVkZ2UpIHsKICAgICAgICAgICAgICByZXR1cm4gZWRnZS50YXJnZXQgIT09IHJlc3VsdC5pZDsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7IC8vIDIuIOiOt+WPlui3r+eUseWZqOWIl+ihqAoKICAgICAgICB2YXIgcm91dGVyTGlzdCA9IHRvcG9EYXRhLm5vZGVzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0uY2F0ZWdvcnkgPT09IDM7CiAgICAgICAgfSk7IC8vIDMuIOWIpOaWrei3r+eUseWZqOaYr+WQpuWtpOeri++8jOWmguaenOWtpOeri+WImeWIoOmZpAoKICAgICAgICByb3V0ZXJMaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHZhciBpc0RlbCA9IHRvcG9EYXRhLmVkZ2VzLmV2ZXJ5KGZ1bmN0aW9uICh2YWwpIHsKICAgICAgICAgICAgcmV0dXJuIHZhbC5zb3VyY2UgIT09IGl0ZW0uaWQ7CiAgICAgICAgICB9KTsKCiAgICAgICAgICBpZiAoaXNEZWwpIHsKICAgICAgICAgICAgdG9wb0RhdGEubm9kZXMgPSB0b3BvRGF0YS5ub2Rlcy5maWx0ZXIoZnVuY3Rpb24gKG5vZGUpIHsKICAgICAgICAgICAgICByZXR1cm4gbm9kZS5pZCAhPT0gaXRlbS5pZDsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIF90aGlzOS5zYXZlVG9wb0RhdGFUb1NlcnZlcih0b3BvRGF0YSk7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, null]}