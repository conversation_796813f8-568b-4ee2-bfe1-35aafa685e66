{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\BackupRestore\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\BackupRestore\\index.vue", "mtime": 1750149101239}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}