{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\InspectionManage\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\InspectionManage\\index.vue", "mtime": 1750152278690}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}