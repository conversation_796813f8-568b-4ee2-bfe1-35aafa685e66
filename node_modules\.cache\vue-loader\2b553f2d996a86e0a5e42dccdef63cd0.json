{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue", "mtime": 1750059025270}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}