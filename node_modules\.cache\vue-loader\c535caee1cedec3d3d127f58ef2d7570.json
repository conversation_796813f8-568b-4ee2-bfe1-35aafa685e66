{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue", "mtime": 1750150748253}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0RmlyZXdhbGxEZXZpY2VMaXN0LAogIGRlbGV0ZUZpcmV3YWxsRGV2aWNlLAogIGJhdGNoRGVsZXRlRmlyZXdhbGxEZXZpY2UsCiAgZGV2aWNlUGluZywKICBnZXRUb3BvRGF0YSwKICBzZXRUb3BvRGF0YSwKfSBmcm9tICdAL2FwaS9maXJld2FsbC9kZXZpY2VNYW5hZ2VtZW50JwppbXBvcnQgQWRkRGV2aWNlTW9kYWwgZnJvbSAnLi9jb21wb25lbnRzL0FkZERldmljZU1vZGFsLnZ1ZScKaW1wb3J0IFVzZXJNYW5hZ2VNb2RhbCBmcm9tICcuL2NvbXBvbmVudHMvVXNlck1hbmFnZU1vZGFsLnZ1ZScKCi8vIOeUn+aIkOWUr+S4gElE55qE5bel5YW35Ye95pWwCmZ1bmN0aW9uIGd1aWQoKSB7CiAgcmV0dXJuICd4eHh4eHh4eC14eHh4LTR4eHgteXh4eC14eHh4eHh4eHh4eHgnLnJlcGxhY2UoL1t4eV0vZywgZnVuY3Rpb24oYykgewogICAgY29uc3QgciA9IChNYXRoLnJhbmRvbSgpICogMTYpIHwgMAogICAgY29uc3QgdiA9IGMgPT09ICd4JyA/IHIgOiAociAmIDB4MykgfCAweDgKICAgIHJldHVybiB2LnRvU3RyaW5nKDE2KQogIH0pCn0KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGV2aWNlTGlzdCcsCiAgY29tcG9uZW50czogewogICAgQWRkRGV2aWNlTW9kYWwsCiAgICBVc2VyTWFuYWdlTW9kYWwsCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlVGFiOiAnMCcsCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBxdWVyeUlucHV0OiB7CiAgICAgICAgZmlyZU5hbWU6ICcnLAogICAgICAgIGlwOiAnJywKICAgICAgICBvbmxpblN0YXR1czogJycsCiAgICAgIH0sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIHNlbGVjdGVkUm93czogW10sCiAgICAgIHBhZ2luYXRpb246IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgdmlzaWJsZTogdHJ1ZSwKICAgICAgfSwKICAgICAgYWRkTW9kYWxWaXNpYmxlOiBmYWxzZSwKICAgICAgdXNlck1vZGFsVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnREYXRhOiBudWxsLAogICAgICBwYW5lczogW10sIC8vIOWKqOaAgeagh+etvumhtQogICAgICB0aW1lcjogbnVsbCwgLy8g5a6a5pe25ZmoCiAgICAgIHRvcG9EYXRhOiB7fSwgLy8g5a2Y5YKo5ouT5omR5pWw5o2uCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoKQogICAgdGhpcy5zdGFydFRpbWVyKCkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLmNsZWFyVGltZXIoKQogIH0sCiAgbWV0aG9kczogewogICAgdG9nZ2xlU2hvdygpIHsKICAgICAgdGhpcy5pc1Nob3cgPSAhdGhpcy5pc1Nob3cKICAgIH0sCiAgICBzdGFydFRpbWVyKCkgewogICAgICAvLyDmr48zMOenkuWIt+aWsOS4gOasoeiuvuWkh+WIl+ihqAogICAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkKICAgICAgfSwgMzAwMDApCiAgICB9LAogICAgY2xlYXJUaW1lcigpIHsKICAgICAgaWYgKHRoaXMudGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMudGltZXIpCiAgICAgICAgdGhpcy50aW1lciA9IG51bGwKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGdldEZpcmV3YWxsRGV2aWNlTGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCBwYXlsb2FkID0gewogICAgICAgIF9saW1pdDogdGhpcy5wYWdpbmF0aW9uLnBhZ2VTaXplLAogICAgICAgIF9wYWdlOiB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UsCiAgICAgICAgcXVlcnlQYXJhbXM6IHRoaXMuYnVpbGRRdWVyeVBhcmFtcygpLAogICAgICAgIHR5cGU6IDEsCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KHBheWxvYWQpCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHJlcy5kYXRhLml0ZW1zIHx8IFtdCiAgICAgICAgICB0aGlzLnBhZ2luYXRpb24udG90YWwgPSByZXMuZGF0YS50b3RhbCB8fCAwCiAgICAgICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluiuvuWkh+WIl+ihqOWksei0pScpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIGJ1aWxkUXVlcnlQYXJhbXMoKSB7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHt9CiAgICAgIGlmICh0aGlzLnF1ZXJ5SW5wdXQuZmlyZU5hbWUpIHBhcmFtcy5maXJlTmFtZSA9IHRoaXMucXVlcnlJbnB1dC5maXJlTmFtZQogICAgICBpZiAodGhpcy5xdWVyeUlucHV0LmlwKSBwYXJhbXMub3JpZ2luSXAgPSB0aGlzLnF1ZXJ5SW5wdXQuaXAKICAgICAgaWYgKHRoaXMucXVlcnlJbnB1dC5vbmxpblN0YXR1cyAhPT0gJycpIHBhcmFtcy5vbmxpblN0YXR1cyA9IHRoaXMucXVlcnlJbnB1dC5vbmxpblN0YXR1cwogICAgICByZXR1cm4gcGFyYW1zCiAgICB9LAogICAgaGFuZGxlVGFiQ2xpY2sodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiLm5hbWUKICAgICAgaWYgKHRhYi5uYW1lICE9PSAnMCcpIHsKICAgICAgICAvLyDlpITnkIbliqjmgIHmoIfnrb7pobXngrnlh7sKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkKICAgIH0sCiAgICBoYW5kbGVSZXNldCgpIHsKICAgICAgdGhpcy5xdWVyeUlucHV0ID0gewogICAgICAgIGZpcmVOYW1lOiAnJywKICAgICAgICBpcDogJycsCiAgICAgICAgb25saW5TdGF0dXM6ICcnLAogICAgICB9CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5jdXJyZW50RGF0YSA9IG51bGwKICAgICAgdGhpcy5hZGRNb2RhbFZpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlRWRpdChyZWNvcmQpIHsKICAgICAgdGhpcy5jdXJyZW50RGF0YSA9IHJlY29yZAogICAgICB0aGlzLmFkZE1vZGFsVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICBoYW5kbGVWaWV3KHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLnN0YXR1cyA9PSAxKSB7CiAgICAgICAgLy8g5omT5byA6K6+5aSH566h55CG6aG16Z2iCiAgICAgICAgd2luZG93Lm9wZW4oYGh0dHBzOi8vJHtyZWNvcmQuaXB9YCkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCforr7lpIfkuI3lnKjnur/vvIzml6Dms5Xmn6XnnIshJykKICAgICAgfQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyZWNvcmQpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6K+l6K6+5aSH5ZCXP+WIoOmZpOWQjuS4jeWPr+aBouWkjScsICfliKDpmaQnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7orqQnLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgfSkKICAgICAgICAudGhlbihhc3luYyAoKSA9PiB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBkZWxldGVGaXJld2FsbERldmljZSh7IGRldmljZV9pZDogcmVjb3JkLmlkIH0pCiAgICAgICAgICAgIGlmIChyZXMucmV0Y29kZSA9PT0gMCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpCiAgICAgICAgICAgICAgLy8g5LuO5ouT5omR5Lit5Yig6Zmk6K6+5aSH6IqC54K5CiAgICAgICAgICAgICAgdGhpcy5kZWxEZXZpY2VOb2RlKFtyZWNvcmQuaXBdKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZykKICAgICAgICAgICAgfQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSlJykKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSkKICAgIH0sCiAgICBoYW5kbGVCYXRjaERlbGV0ZSgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3dzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iHs+WwkemAieS4reS4gOadoeaVsOaNricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOmAieS4reiuvuWkh+WQlz/liKDpmaTlkI7kuI3lj6/mgaLlpI0nLCAn5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu6K6kJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgIH0pCiAgICAgICAgLnRoZW4oYXN5bmMgKCkgPT4gewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgY29uc3QgaWRzID0gdGhpcy5zZWxlY3RlZFJvd3MubWFwKChyb3cpID0+IHJvdy5pZCkKICAgICAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgYmF0Y2hEZWxldGVGaXJld2FsbERldmljZSh7IGRldmljZV9pZHM6IGlkcyB9KQogICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpCiAgICAgICAgICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgICAgICAgICAgIC8vIOS7juaLk+aJkeS4reaJuemHj+WIoOmZpOiuvuWkh+iKgueCuQogICAgICAgICAgICAgIGNvbnN0IGRldmljZUlwcyA9IHRoaXMuc2VsZWN0ZWRSb3dzLm1hcCgocm93KSA9PiByb3cuaXApCiAgICAgICAgICAgICAgdGhpcy5kZWxEZXZpY2VOb2RlKGRldmljZUlwcykKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4ge30pCiAgICB9LAogICAgYXN5bmMgaGFuZGxlUGluZyhyZWNvcmQpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOa1i+ivlei/nuaOpS4uLicpCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZGV2aWNlUGluZyh7IGlwOiByZWNvcmQuaXAgfSkKICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6K6+5aSH6L+e5o6l5q2j5bi4JykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K6+5aSH6L+e5o6l5aSx6LSlJykKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6L+e5o6l5rWL6K+V5aSx6LSlJykKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVVzZXJNYW5hZ2UocmVjb3JkKSB7CiAgICAgIHRoaXMuY3VycmVudERhdGEgPSByZWNvcmQKICAgICAgdGhpcy51c2VyTW9kYWxWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZUFkZFN1Ym1pdChkZXZpY2VEYXRhKSB7CiAgICAgIHRoaXMuYWRkTW9kYWxWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgICAvLyDlpoLmnpzmnInorr7lpIfmlbDmja7vvIzmt7vliqDliLDmi5PmiZHkuK0KICAgICAgaWYgKGRldmljZURhdGEgJiYgZGV2aWNlRGF0YS5pcCkgewogICAgICAgIHRoaXMuYWRkRGV2aWNlTm9kZShkZXZpY2VEYXRhKQogICAgICB9CiAgICB9LAogICAgaGFuZGxlVXNlclN1Ym1pdCgpIHsKICAgICAgdGhpcy51c2VyTW9kYWxWaXNpYmxlID0gZmFsc2UKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uCiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5wYWdlU2l6ZSA9IHNpemUKICAgICAgdGhpcy5nZXRGaXJld2FsbERldmljZUxpc3QoKQogICAgfSwKICAgIGhhbmRsZVBhZ2VDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSBwYWdlCiAgICAgIHRoaXMuZ2V0RmlyZXdhbGxEZXZpY2VMaXN0KCkKICAgIH0sCgogICAgLy8gPT09PT09PT09PT09PT09PT09PT0g5ouT5omR55u45YWz5pa55rOVID09PT09PT09PT09PT09PT09PT09CgogICAgLyoqCiAgICAgKiDojrflj5bmi5PmiZHmlbDmja4KICAgICAqLwogICAgYXN5bmMgZ2V0VG9wb0RhdGFGcm9tU2VydmVyKGNhbGxiYWNrKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0VG9wb0RhdGEoKQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgdGhpcy50b3BvRGF0YSA9IHJlcy5kYXRhIHx8IHsgbm9kZXM6IFtdLCBlZGdlczogW10gfQogICAgICAgICAgaWYgKGNhbGxiYWNrKSBjYWxsYmFjaygpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaLk+aJkeaVsOaNruWksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLyoqCiAgICAgKiDkv53lrZjmi5PmiZHmlbDmja4KICAgICAqIEBwYXJhbSB0b3BvRGF0YQogICAgICovCiAgICBhc3luYyBzYXZlVG9wb0RhdGFUb1NlcnZlcih0b3BvRGF0YSkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHNldFRvcG9EYXRhKHsKICAgICAgICAgIHRvcG9sb2d5X3RleHQ6IHRvcG9EYXRhLAogICAgICAgIH0pCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICB0aGlzLmdldEZpcmV3YWxsRGV2aWNlTGlzdCgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOaLk+aJkeaVsOaNruWksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLyoqCiAgICAgKiDnlJ/miJDorr7lpIfoioLngrkKICAgICAqLwogICAgZ2VuZXJhdGVOb2RlKG9wdGlvbnMpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBpZDogZ3VpZCgpLAogICAgICAgIHR5cGU6ICdub2RlJywKICAgICAgICBzaXplOiAnNTAnLAogICAgICAgIHNoYXBlOiAna29uaS1jdXN0b20tbm9kZScsCiAgICAgICAgY29sb3I6ICcjNjlDMEZGJywKICAgICAgICBsYWJlbE9mZnNldFk6IDM4LAogICAgICAgIC4uLm9wdGlvbnMsCiAgICAgIH0KICAgIH0sCgogICAgLyoqCiAgICAgKiDmt7vliqDorr7lpIfoioLngrnlip/og70KICAgICAqIEBwYXJhbSB2YWx1ZXMKICAgICAqLwogICAgYWRkRGV2aWNlTm9kZSh2YWx1ZXMpIHsKICAgICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoKCkgPT4gewogICAgICAgIGNvbnN0IHRvcG9EYXRhID0geyAuLi50aGlzLnRvcG9EYXRhIH0KCiAgICAgICAgLy8g56Gu5L+dIG5vZGVzIOWSjCBlZGdlcyDmlbDnu4TlrZjlnKgKICAgICAgICBpZiAoIXRvcG9EYXRhLm5vZGVzKSB0b3BvRGF0YS5ub2RlcyA9IFtdCiAgICAgICAgaWYgKCF0b3BvRGF0YS5lZGdlcykgdG9wb0RhdGEuZWRnZXMgPSBbXQoKICAgICAgICAvLyAxLiDliKTmlq3ot6/nlLHlmajmmK/lkKblrZjlnKjvvIzkuI3lrZjlnKjnlJ/miJDot6/nlLHlmagKICAgICAgICBjb25zdCB7IGlwIH0gPSB2YWx1ZXMKICAgICAgICBjb25zdCBpcGxpc3QgPSBpcC5zcGxpdCgnLicpCiAgICAgICAgaXBsaXN0LnBvcCgpCiAgICAgICAgY29uc3Qgcm91dGVySXAgPSBpcGxpc3Quam9pbignLicpICsgJy4wJwoKICAgICAgICBjb25zdCBpc0V4aXN0ID0gdG9wb0RhdGEubm9kZXMubGVuZ3RoID4gMCAmJiB0b3BvRGF0YS5ub2Rlcy5zb21lKCh2YWwpID0+IHZhbC5kZXZpY2VfaXAgPT09IHJvdXRlcklwKQoKICAgICAgICBpZiAoIWlzRXhpc3QpIHsKICAgICAgICAgIGNvbnN0IG5ld05vZGUgPSB0aGlzLmdlbmVyYXRlTm9kZSh7CiAgICAgICAgICAgIGRldmljZV9pcDogcm91dGVySXAsCiAgICAgICAgICAgIGNhdGVnb3J5OiAzLAogICAgICAgICAgICBsYWJlbDogJ+i3r+eUseWZqCcsCiAgICAgICAgICB9KQogICAgICAgICAgdG9wb0RhdGEubm9kZXMucHVzaChuZXdOb2RlKQogICAgICAgIH0KCiAgICAgICAgLy8gMi4g5Yik5pat6K6+5aSH5piv5ZCm5a2Y5Zyo77yM5LiN5a2Y5Zyo5bCx55Sf5oiQ6K6+5aSH6IqC54K55ZKM6L+e57q/6L65CiAgICAgICAgY29uc3QgaXNEZXZFeGlzdCA9IHRvcG9EYXRhLm5vZGVzLmxlbmd0aCA+IDAgJiYgdG9wb0RhdGEubm9kZXMuc29tZSgodmFsKSA9PiB2YWwuZGV2aWNlX2lwID09PSB2YWx1ZXMuaXApCgogICAgICAgIGlmICghaXNEZXZFeGlzdCkgewogICAgICAgICAgY29uc3QgbmV3Tm9kZSA9IHRoaXMuZ2VuZXJhdGVOb2RlKHsKICAgICAgICAgICAgbGFiZWw6ICfpmLLngavlopknLAogICAgICAgICAgICBjYXRlZ29yeTogMSwKICAgICAgICAgICAgZGV2aWNlX2lwOiB2YWx1ZXMuaXAsCiAgICAgICAgICB9KQogICAgICAgICAgdG9wb0RhdGEubm9kZXMucHVzaChuZXdOb2RlKQoKICAgICAgICAgIGNvbnN0IHNlcnZlckRhdGEgPSB0b3BvRGF0YS5ub2Rlcy5maW5kKCh2YWwpID0+IHZhbC5kZXZpY2VfaXAgPT09IHJvdXRlcklwKQogICAgICAgICAgaWYgKHNlcnZlckRhdGEpIHsKICAgICAgICAgICAgdG9wb0RhdGEuZWRnZXMucHVzaCh7CiAgICAgICAgICAgICAgaWQ6IGd1aWQoKSwKICAgICAgICAgICAgICBzb3VyY2U6IHNlcnZlckRhdGEuaWQsCiAgICAgICAgICAgICAgdGFyZ2V0OiBuZXdOb2RlLmlkLAogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgdGhpcy5zYXZlVG9wb0RhdGFUb1NlcnZlcih0b3BvRGF0YSkKICAgICAgfSkKICAgIH0sCgogICAgLyoqCiAgICAgKiDliKDpmaTorr7lpIfoioLngrnlip/og70KICAgICAqIEBwYXJhbSBzZWxlY3RlZERldkxpc3QKICAgICAqLwogICAgZGVsRGV2aWNlTm9kZShzZWxlY3RlZERldkxpc3QpIHsKICAgICAgdGhpcy5nZXRUb3BvRGF0YUZyb21TZXJ2ZXIoKCkgPT4gewogICAgICAgIGNvbnN0IHRvcG9EYXRhID0geyAuLi50aGlzLnRvcG9EYXRhIH0KCiAgICAgICAgLy8g56Gu5L+dIG5vZGVzIOWSjCBlZGdlcyDmlbDnu4TlrZjlnKgKICAgICAgICBpZiAoIXRvcG9EYXRhLm5vZGVzKSB0b3BvRGF0YS5ub2RlcyA9IFtdCiAgICAgICAgaWYgKCF0b3BvRGF0YS5lZGdlcykgdG9wb0RhdGEuZWRnZXMgPSBbXQoKICAgICAgICAvLyAxLiDliKTmlq3orr7lpIfmmK/lkKblrZjlnKjvvIzlrZjlnKjlsLHliKDpmaToioLngrnlkozov57nur/ovrkKICAgICAgICBzZWxlY3RlZERldkxpc3QuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgICAgY29uc3QgcmVzdWx0ID0gdG9wb0RhdGEubm9kZXMuZmluZCgodmFsKSA9PiB2YWwuZGV2aWNlX2lwID09PSBpdGVtKQogICAgICAgICAgaWYgKHJlc3VsdCkgewogICAgICAgICAgICB0b3BvRGF0YS5ub2RlcyA9IHRvcG9EYXRhLm5vZGVzLmZpbHRlcigobm9kZSkgPT4gbm9kZS5pZCAhPT0gcmVzdWx0LmlkKQogICAgICAgICAgICB0b3BvRGF0YS5lZGdlcyA9IHRvcG9EYXRhLmVkZ2VzLmZpbHRlcigoZWRnZSkgPT4gZWRnZS50YXJnZXQgIT09IHJlc3VsdC5pZCkKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgICAvLyAyLiDojrflj5bot6/nlLHlmajliJfooagKICAgICAgICBjb25zdCByb3V0ZXJMaXN0ID0gdG9wb0RhdGEubm9kZXMuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmNhdGVnb3J5ID09PSAzKQoKICAgICAgICAvLyAzLiDliKTmlq3ot6/nlLHlmajmmK/lkKblraTnq4vvvIzlpoLmnpzlraTnq4vliJnliKDpmaQKICAgICAgICByb3V0ZXJMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICAgIGNvbnN0IGlzRGVsID0gdG9wb0RhdGEuZWRnZXMuZXZlcnkoKHZhbCkgPT4gdmFsLnNvdXJjZSAhPT0gaXRlbS5pZCkKICAgICAgICAgIGlmIChpc0RlbCkgewogICAgICAgICAgICB0b3BvRGF0YS5ub2RlcyA9IHRvcG9EYXRhLm5vZGVzLmZpbHRlcigobm9kZSkgPT4gbm9kZS5pZCAhPT0gaXRlbS5pZCkKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgICB0aGlzLnNhdmVUb3BvRGF0YVRvU2VydmVyKHRvcG9EYXRhKQogICAgICB9KQogICAgfSwKICB9LAp9Cg=="}, null]}