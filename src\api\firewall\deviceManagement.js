import request from '@/util/requestForPy'

/**
 * 获取防火墙设备列表
 * @param params
 * @returns {Promise}
 */
export function getFirewallDeviceList(params) {
  let newParams = {}
  if (params.queryParams) {
    const queryParams = params.queryParams
    if (queryParams.fireName) newParams.name = queryParams.fireName
    if (queryParams.group_id) newParams.group_id = queryParams.group_id
    if (queryParams.originIp) newParams.ip = queryParams.originIp
    if (queryParams.onlinStatus) newParams.status = queryParams.onlinStatus
  }
  newParams.category = params.type
  newParams.page = params._page
  newParams.per_page = params._limit
  return request({
    url: '/api2/device/list',
    method: 'get',
    params: newParams || {},
  })
}

/**
 * 新增防火墙设备
 * @param data
 * @returns {Promise}
 */
export function addFirewallDevice(data) {
  return request({
    url: '/api2/device/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 编辑防火墙设备
 * @param data
 * @returns {Promise}
 */
export function updateFirewallDevice(data) {
  return request({
    url: '/api2/device/edit',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除防火墙设备
 * @param data
 * @returns {Promise}
 */
export function deleteFirewallDevice(data) {
  return request({
    url: '/api2/device/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 批量删除防火墙设备
 * @param data
 * @returns {Promise}
 */
export function batchDeleteFirewallDevice(data) {
  return request({
    url: '/api2/device/batchDelete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 设备Ping测试
 * @param data
 * @returns {Promise}
 */
export function devicePing(data) {
  return request({
    url: '/api2/device/ping',
    method: 'post',
    data: data || {},
  })
}

/**
 * 获取拓扑数据
 * @param params
 * @returns {Promise}
 */
export function getTopoData(params) {
  return request({
    url: '/dev/topo/get',
    method: 'post',
    data: params || {},
  })
}

/**
 * 保存拓扑数据
 * @param params
 * @returns {Promise}
 */
export function setTopoData(params) {
  return request({
    url: '/dev/topo/set',
    method: 'post',
    data: params || {},
  })
}

/**
 * 获取设备用户列表
 * @param params
 * @returns {Promise}
 */
export function getDeviceUserList(params) {
  return request({
    url: '/api2/device/user/list',
    method: 'post',
    data: params || {},
  })
}

/**
 * 添加设备用户
 * @param data
 * @returns {Promise}
 */
export function addDeviceUser(data) {
  return request({
    url: '/api2/device/user/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 删除设备用户
 * @param data
 * @returns {Promise}
 */
export function deleteDeviceUser(data) {
  return request({
    url: '/api2/device/user/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 登录日志记录
 * @param data
 * @returns {Promise}
 */
export function saveLoginLog(data) {
  return request({
    url: '/api2/device/login/log',
    method: 'post',
    data: data || {},
  })
}

/**
 * 获取设备树数据
 * @param params
 * @returns {Promise}
 */
export function getDeviceTreeData(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}
