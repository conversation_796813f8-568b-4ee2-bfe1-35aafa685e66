import request from '@/util/requestForPy'

/**
 * 巡检管理-查询接口
 * @param params
 * @returns {Promise}
 */
export function getInspectionList(params) {
  return request({
    url: '/dev/inspection/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 巡检管理-新增接口
 * @param data
 * @returns {Promise}
 */
export function addInspection(data) {
  return request({
    url: '/dev/inspection/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-编辑接口
 * @param data
 * @returns {Promise}
 */
export function updateInspection(data) {
  return request({
    url: '/dev/inspection/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteInspection(data) {
  return request({
    url: '/dev/inspection/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-启动巡检
 * @param data
 * @returns {Promise}
 */
export function startInspection(data) {
  return request({
    url: '/dev/inspection/start',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-停止巡检
 * @param data
 * @returns {Promise}
 */
export function stopInspection(data) {
  return request({
    url: '/dev/inspection/stop',
    method: 'post',
    data: data || {},
  })
}

export function getDeviceList(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}
