import request from '@/util/requestForPy'

/**
 * 巡检管理-查询接口
 * @param params
 * @returns {Promise}
 */
export function getInspectionList(params) {
  return request({
    url: '/dev/inspection/pages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 巡检管理-新增接口
 * @param data
 * @returns {Promise}
 */
export function addInspection(data) {
  return request({
    url: '/dev/inspection/add',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-编辑接口
 * @param data
 * @returns {Promise}
 */
export function updateInspection(data) {
  return request({
    url: '/dev/inspection/update',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-删除接口
 * @param data
 * @returns {Promise}
 */
export function deleteInspection(data) {
  return request({
    url: '/dev/inspection/delete',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-启动巡检
 * @param data
 * @returns {Promise}
 */
export function startInspection(data) {
  return request({
    url: '/dev/inspection/start',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-停止巡检
 * @param data
 * @returns {Promise}
 */
export function stopInspection(data) {
  return request({
    url: '/dev/inspection/stop',
    method: 'post',
    data: data || {},
  })
}

export function getDeviceList(params) {
  return request({
    url: '/dev/device/all',
    method: 'post',
    data: params || {},
  })
}

/**
 * 巡检管理-获取设备数据（用于新建巡检）
 * @param params
 * @returns {Promise}
 */
export function addInspectionData(params) {
  return request({
    url: '/dev/inspection/addInspectionData',
    method: 'post',
    data: params || {},
  })
}

/**
 * 巡检管理-保存巡检数据
 * @param data
 * @returns {Promise}
 */
export function addInspectionSaveData(data) {
  return request({
    url: '/dev/inspection/addInspectionSave',
    method: 'post',
    data: data || {},
  })
}

/**
 * 巡检管理-巡检结果分页查询
 * @param params
 * @returns {Promise}
 */
export function inspectionResultPages(params) {
  return request({
    url: '/dev/inspection/resultPages',
    method: 'post',
    data: params || {},
  })
}

/**
 * 巡检管理-查看巡检详情
 * @param params
 * @returns {Promise}
 */
export function inspectionInfo(params) {
  return request({
    url: '/dev/inspection/info',
    method: 'post',
    data: params || {},
  })
}

/**
 * 巡检管理-导出Excel
 * @param params
 * @returns {Promise}
 */
export function downloadExcel(params) {
  return request({
    url: '/dev/inspection/downloadExcel',
    method: 'post',
    data: params || {},
    responseType: 'blob',
  })
}
