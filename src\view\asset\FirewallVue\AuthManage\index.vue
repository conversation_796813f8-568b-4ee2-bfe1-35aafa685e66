<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.deviceName" clearable placeholder="设备名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="primary" @click="handleAdd">新建授权</el-button>
          <el-button type="primary" @click="handleSync">设备同步</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.deviceName" clearable placeholder="设备名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.authStatus" clearable placeholder="授权状态" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="已授权" value="1"></el-option>
                  <el-option label="未授权" value="0"></el-option>
                  <el-option label="过期" value="2"></el-option>
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.licenseType" clearable placeholder="许可证类型" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="IPS" value="IPS"></el-option>
                  <el-option label="SOFT" value="SOFT"></el-option>
                  <el-option label="AntiVirus" value="AntiVirus"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">授权管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="liceneStatus" label="授权状态">
            <template slot-scope="scope">
              <span :class="getStatusClass(scope.row.liceneStatus)">
                {{ getStatusText(scope.row.liceneStatus) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="licenceCategory" label="许可证类型"></el-table-column>
          <el-table-column prop="expireDate" label="到期时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.expireDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="获取设备许可证时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleReauthorize(scope.row)">重新授权</el-button>
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>

    <!-- 新增授权对话框 -->
    <add-auth-modal :visible.sync="addModalVisible" @on-submit="handleAddSubmit"></add-auth-modal>

    <!-- 设备同步组件 -->
    <device-sync-component ref="deviceSyncComponent" @on-submit="handleSyncSubmit"></device-sync-component>
  </div>
</template>

<script>
import { getAuthList, deleteAuth, reauthorize, syncAuthFromDevice } from '@/api/firewall/authManagement'
import AddAuthModal from './components/AddAuthModal.vue'
import DeviceSyncComponent from './components/DeviceSyncComponent.vue'
import dayjs from 'dayjs'

export default {
  name: 'AuthManage',
  components: {
    AddAuthModal,
    DeviceSyncComponent,
  },
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        deviceName: '',
        authStatus: '',
        licenseType: '',
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addModalVisible: false,
    }
  },
  mounted() {
    this.getAuthList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getAuthList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getAuthList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取授权列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.deviceName) params.deviceName = this.queryInput.deviceName
      if (this.queryInput.authStatus !== '') params.authStatus = this.queryInput.authStatus
      if (this.queryInput.licenseType) params.licenseType = this.queryInput.licenseType
      return params
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getAuthList()
    },
    handleReset() {
      this.queryInput = {
        deviceName: '',
        authStatus: '',
        licenseType: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.addModalVisible = true
    },
    handleSync() {
      this.$refs.deviceSyncComponent.showDrawer()
    },
    handleReauthorize(record) {
      this.$confirm('确定要重新授权该设备吗?', '重新授权', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await reauthorize({ id: record.id })
            if (res.retcode === 0) {
              this.$message.success('重新授权成功')
              this.getAuthList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('重新授权失败')
          }
        })
        .catch(() => {})
    },
    handleDelete(record) {
      this.$confirm('确定要删除该授权吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteAuth({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getAuthList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中授权吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            const res = await deleteAuth({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getAuthList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleAddSubmit() {
      this.addModalVisible = false
      this.getAuthList()
    },
    handleSyncSubmit() {
      this.getAuthList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getAuthList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getAuthList()
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY/MM/DD')
    },
    getStatusText(status) {
      const statusMap = {
        '0': '未授权',
        '1': '已授权',
        '2': '过期',
      }
      return statusMap[status] || '未知'
    },
    getStatusClass(status) {
      const classMap = {
        '0': 'status-failed',
        '1': 'status-success',
        '2': 'status-warning',
      }
      return classMap[status] || ''
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.status-warning {
  color: #e6a23c;
}
</style>
