<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.backupName" clearable placeholder="备份名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="primary" @click="handleCreateBackup">创建备份</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.backupName" clearable placeholder="备份名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.backupType" clearable placeholder="备份类型" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="完整备份" value="full"></el-option>
                  <el-option label="配置备份" value="config"></el-option>
                  <el-option label="策略备份" value="policy"></el-option>
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="queryInput.createTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  @change="handleQuery"
                />
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">备份管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="backupName" label="备份名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="backupType" label="备份类型">
            <template slot-scope="scope">
              {{ getBackupTypeText(scope.row.backupType) }}
            </template>
          </el-table-column>
          <el-table-column prop="fileSize" label="文件大小"></el-table-column>
          <el-table-column prop="deviceCount" label="设备数量"></el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="getStatusClass(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleRestore(scope.row)" :disabled="scope.row.status !== 'success'">还原</el-button>
                <el-button class="el-button--blue" type="text" @click="handleDownload(scope.row)" :disabled="scope.row.status !== 'success'">下载</el-button>
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>

    <!-- 创建备份对话框 -->
    <backup-modal :visible.sync="backupModalVisible" @on-submit="handleBackupSubmit"></backup-modal>

    <!-- 还原备份对话框 -->
    <restore-modal :visible.sync="restoreModalVisible" :current-backup="currentBackup" @on-submit="handleRestoreSubmit"></restore-modal>
  </div>
</template>

<script>
import { getBackupList, deleteBackup, downloadBackup } from '@/api/firewall/backupRestore'
import BackupModal from './components/BackupModal.vue'
import RestoreModal from './components/RestoreModal.vue'
import dayjs from 'dayjs'

export default {
  name: 'BackupRestore',
  components: {
    BackupModal,
    RestoreModal,
  },
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        backupName: '',
        backupType: '',
        createTime: null,
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      backupModalVisible: false,
      restoreModalVisible: false,
      currentBackup: {},
    }
  },
  mounted() {
    this.getBackupList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getBackupList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getBackupList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取备份列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.backupName) params.backupName = this.queryInput.backupName
      if (this.queryInput.backupType) params.backupType = this.queryInput.backupType
      if (this.queryInput.createTime && this.queryInput.createTime.length > 0) {
        params.startDate = this.queryInput.createTime[0]
        params.endDate = this.queryInput.createTime[1]
      }
      return params
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getBackupList()
    },
    handleReset() {
      this.queryInput = {
        backupName: '',
        backupType: '',
        createTime: null,
      }
      this.handleQuery()
    },
    handleCreateBackup() {
      this.backupModalVisible = true
    },
    handleRestore(record) {
      this.currentBackup = record
      this.restoreModalVisible = true
    },
    async handleDownload(record) {
      try {
        const res = await downloadBackup({ id: record.id })
        // 处理文件下载
        const blob = new Blob([res])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = record.backupName + '.backup'
        link.click()
        window.URL.revokeObjectURL(url)
      } catch (error) {
        this.$message.error('下载失败')
      }
    },
    handleDelete(record) {
      this.$confirm('确定要删除该备份吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteBackup({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getBackupList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中备份吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            const res = await deleteBackup({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getBackupList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBackupSubmit() {
      this.backupModalVisible = false
      this.getBackupList()
    },
    handleRestoreSubmit() {
      this.restoreModalVisible = false
      this.getBackupList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getBackupList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getBackupList()
    },
    getBackupTypeText(type) {
      const typeMap = {
        'full': '完整备份',
        'config': '配置备份',
        'policy': '策略备份',
      }
      return typeMap[type] || '-'
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '备份中',
        'success': '成功',
        'failed': '失败',
      }
      return statusMap[status] || '-'
    },
    getStatusClass(status) {
      const classMap = {
        'pending': 'status-warning',
        'success': 'status-success',
        'failed': 'status-failed',
      }
      return classMap[status] || ''
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.status-warning {
  color: #e6a23c;
}
</style>
