<template>
  <div class="router-wrap-table">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="设备列表" name="0">
        <header class="table-header">
          <section class="table-header-main">
            <section class="table-header-search">
              <section v-show="!isShow" class="table-header-search-input">
                <el-input
                  v-model="queryInput.fireName"
                  clearable
                  placeholder="设备名称"
                  prefix-icon="soc-icon-search"
                  @change="handleQuery"
                ></el-input>
              </section>
              <section class="table-header-search-button">
                <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="toggleShow">
                  高级搜索
                  <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
                </el-button>
              </section>
            </section>
            <section class="table-header-button">
              <el-button type="primary" @click="handleAdd">新建设备</el-button>
              <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
            </section>
          </section>
          <section class="table-header-extend">
            <el-collapse-transition>
              <div v-show="isShow">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-input v-model="queryInput.fireName" clearable placeholder="设备名称" @change="handleQuery"></el-input>
                  </el-col>
                  <el-col :span="6">
                    <el-input v-model="queryInput.ip" clearable placeholder="设备IP" @change="handleQuery"></el-input>
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="queryInput.onlinStatus" clearable placeholder="在线状态" @change="handleQuery">
                      <el-option label="全部" value=""></el-option>
                      <el-option label="在线" value="1"></el-option>
                      <el-option label="离线" value="0"></el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24" align="right">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                    <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-transition>
          </section>
        </header>

        <main class="table-body">
          <section class="table-body-header">
            <h2 class="table-body-title">防火墙设备管理</h2>
          </section>
          <section v-loading="loading" class="table-body-main">
            <el-table
              :data="tableData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center"></el-table-column>
              <el-table-column label="序号" width="80" align="center">
                <template slot-scope="scope">
                  {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="notes" label="设备名称" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button type="text" class="el-button--blue" @click="handleView(scope.row)">
                    {{ scope.row.notes }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="category_text" label="设备类型"></el-table-column>
              <el-table-column label="在线状态" width="100">
                <template slot-scope="scope">
                  <span :class="scope.row.status === 1 ? 'status-online' : 'status-offline'">
                    <i :class="scope.row.status === 1 ? 'el-icon-success' : 'el-icon-error'"></i>
                    {{ scope.row.status === 1 ? '在线' : '离线' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="syl_cpu" label="CPU率" width="120">
                <template slot-scope="scope">
                  <el-progress v-if="scope.row.syl_cpu" :percentage="parseInt(scope.row.syl_cpu)" :stroke-width="8" color="#52C41A"></el-progress>
                </template>
              </el-table-column>
              <el-table-column prop="syl_nc" label="内存率" width="120">
                <template slot-scope="scope">
                  <el-progress v-if="scope.row.syl_nc" :percentage="parseInt(scope.row.syl_nc)" :stroke-width="8" color="#4C24ED"></el-progress>
                </template>
              </el-table-column>
              <el-table-column prop="syl_disk" label="磁盘率" width="120">
                <template slot-scope="scope">
                  <el-progress v-if="scope.row.syl_disk" :percentage="parseInt(scope.row.syl_disk)" :stroke-width="8" color="#1373F1"></el-progress>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注"></el-table-column>
              <el-table-column label="操作" width="280" fixed="right">
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <el-button class="el-button--blue" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
                    <el-button class="el-button--blue" type="text" @click="handlePing(scope.row)">Ping</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleUserManage(scope.row)">用户管理</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </main>
        <footer class="table-footer">
          <el-pagination
            v-if="pagination.visible"
            small
            background
            align="right"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          ></el-pagination>
        </footer>
      </el-tab-pane>

      <!-- 动态标签页 -->
      <el-tab-pane v-for="pane in panes" :key="pane.name" :label="pane.title" :name="pane.name" :closable="true">
        <iframe :src="pane.content" width="100%" height="600px" frameborder="0"></iframe>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑设备对话框 -->
    <add-device-modal :visible.sync="addModalVisible" :current-data="currentData" @on-submit="handleAddSubmit"></add-device-modal>

    <!-- 用户管理对话框 -->
    <user-manage-modal :visible.sync="userModalVisible" :current-data="currentData" @on-submit="handleUserSubmit"></user-manage-modal>
  </div>
</template>

<script>
import {
  getFirewallDeviceList,
  deleteFirewallDevice,
  batchDeleteFirewallDevice,
  devicePing,
  getTopoData,
  setTopoData,
} from '@/api/firewall/deviceManagement'
import AddDeviceModal from './components/AddDeviceModal.vue'
import UserManageModal from './components/UserManageModal.vue'

// 生成唯一ID的工具函数
function guid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export default {
  name: 'DeviceList',
  components: {
    AddDeviceModal,
    UserManageModal,
  },
  data() {
    return {
      activeTab: '0',
      isShow: false,
      loading: false,
      queryInput: {
        fireName: '',
        ip: '',
        onlinStatus: '',
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addModalVisible: false,
      userModalVisible: false,
      currentData: null,
      panes: [], // 动态标签页
      timer: null, // 定时器
      topoData: {}, // 存储拓扑数据
    }
  },
  mounted() {
    this.getFirewallDeviceList()
    this.getTopoDataFromServer()
    this.startTimer()
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    startTimer() {
      // 每30秒刷新一次设备列表
      this.timer = setInterval(() => {
        this.getFirewallDeviceList()
      }, 30000)
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    async getFirewallDeviceList() {
      this.loading = true
      const payload = {
        _limit: this.pagination.pageSize,
        _page: this.pagination.currentPage,
        queryParams: this.buildQueryParams(),
        type: 1,
      }

      try {
        const res = await getFirewallDeviceList(payload)
        if (res.code === 0) {
          this.tableData = res.data.items || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.fireName) params.fireName = this.queryInput.fireName
      if (this.queryInput.ip) params.originIp = this.queryInput.ip
      if (this.queryInput.onlinStatus !== '') params.onlinStatus = this.queryInput.onlinStatus
      return params
    },
    handleTabClick(tab) {
      this.activeTab = tab.name
      if (tab.name !== '0') {
        // 处理动态标签页点击
      }
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getFirewallDeviceList()
    },
    handleReset() {
      this.queryInput = {
        fireName: '',
        ip: '',
        onlinStatus: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.currentData = null
      this.addModalVisible = true
    },
    handleEdit(record) {
      this.currentData = record
      this.addModalVisible = true
    },
    handleView(record) {
      if (record.status == 1) {
        // 打开设备管理页面
        window.open(`https://${record.ip}`)
      } else {
        this.$message.error('设备不在线，无法查看!')
      }
    },
    handleDelete(record) {
      this.$confirm('确定要删除该设备吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteFirewallDevice({ device_id: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getFirewallDeviceList()
              // 从拓扑中删除设备节点
              this.delDeviceNode([record.ip])
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中设备吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map((row) => row.id)
            const res = await batchDeleteFirewallDevice({ device_ids: ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getFirewallDeviceList()
              // 从拓扑中批量删除设备节点
              const deviceIps = this.selectedRows.map((row) => row.ip)
              this.delDeviceNode(deviceIps)
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    async handlePing(record) {
      try {
        this.$message.info('正在测试连接...')
        const res = await devicePing({ ip: record.ip })
        if (res.retcode === 0) {
          this.$message.success('设备连接正常')
        } else {
          this.$message.error('设备连接失败')
        }
      } catch (error) {
        this.$message.error('连接测试失败')
      }
    },
    handleUserManage(record) {
      this.currentData = record
      this.userModalVisible = true
    },
    handleAddSubmit(deviceData) {
      this.addModalVisible = false
      this.getFirewallDeviceList()
      // 如果有设备数据，添加到拓扑中
      if (deviceData && deviceData.ip) {
        this.addDeviceNode(deviceData)
      }
    },
    handleUserSubmit() {
      this.userModalVisible = false
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getFirewallDeviceList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getFirewallDeviceList()
    },

    // ==================== 拓扑相关方法 ====================

    /**
     * 获取拓扑数据
     */
    async getTopoDataFromServer(callback) {
      try {
        const res = await getTopoData()
        if (res.code === 0) {
          this.topoData = res.data || { nodes: [], edges: [] }
          if (callback) callback()
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error('获取拓扑数据失败')
      }
    },

    /**
     * 保存拓扑数据
     * @param topoData
     */
    async saveTopoDataToServer(topoData) {
      try {
        const res = await setTopoData({
          topology_text: topoData,
        })
        if (res.code === 0) {
          this.getFirewallDeviceList()
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error('保存拓扑数据失败')
      }
    },

    /**
     * 生成设备节点
     */
    generateNode(options) {
      return {
        id: guid(),
        type: 'node',
        size: '50',
        shape: 'koni-custom-node',
        color: '#69C0FF',
        labelOffsetY: 38,
        ...options,
      }
    },

    /**
     * 添加设备节点功能
     * @param values
     */
    addDeviceNode(values) {
      this.getTopoDataFromServer(() => {
        const topoData = { ...this.topoData }

        // 确保 nodes 和 edges 数组存在
        if (!topoData.nodes) topoData.nodes = []
        if (!topoData.edges) topoData.edges = []

        // 1. 判断路由器是否存在，不存在生成路由器
        const { ip } = values
        const iplist = ip.split('.')
        iplist.pop()
        const routerIp = iplist.join('.') + '.0'

        const isExist = topoData.nodes.length > 0 && topoData.nodes.some((val) => val.device_ip === routerIp)

        if (!isExist) {
          const newNode = this.generateNode({
            device_ip: routerIp,
            category: 3,
            label: '路由器',
          })
          topoData.nodes.push(newNode)
        }

        // 2. 判断设备是否存在，不存在就生成设备节点和连线边
        const isDevExist = topoData.nodes.length > 0 && topoData.nodes.some((val) => val.device_ip === values.ip)

        if (!isDevExist) {
          const newNode = this.generateNode({
            label: '防火墙',
            category: 1,
            device_ip: values.ip,
          })
          topoData.nodes.push(newNode)

          const serverData = topoData.nodes.find((val) => val.device_ip === routerIp)
          if (serverData) {
            topoData.edges.push({
              id: guid(),
              source: serverData.id,
              target: newNode.id,
            })
          }
        }

        this.saveTopoDataToServer(topoData)
      })
    },

    /**
     * 删除设备节点功能
     * @param selectedDevList
     */
    delDeviceNode(selectedDevList) {
      this.getTopoDataFromServer(() => {
        const topoData = { ...this.topoData }

        // 确保 nodes 和 edges 数组存在
        if (!topoData.nodes) topoData.nodes = []
        if (!topoData.edges) topoData.edges = []

        // 1. 判断设备是否存在，存在就删除节点和连线边
        selectedDevList.forEach((item) => {
          const result = topoData.nodes.find((val) => val.device_ip === item)
          if (result) {
            topoData.nodes = topoData.nodes.filter((node) => node.id !== result.id)
            topoData.edges = topoData.edges.filter((edge) => edge.target !== result.id)
          }
        })

        // 2. 获取路由器列表
        const routerList = topoData.nodes.filter((item) => item.category === 3)

        // 3. 判断路由器是否孤立，如果孤立则删除
        routerList.forEach((item) => {
          const isDel = topoData.edges.every((val) => val.source !== item.id)
          if (isDel) {
            topoData.nodes = topoData.nodes.filter((node) => node.id !== item.id)
          }
        })

        this.saveTopoDataToServer(topoData)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.router-wrap-table {
  padding: 8px 24px;
  .table-header {
    background-color: transparent;
  }
}
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-online {
  color: #67c23a;
}

.status-offline {
  color: #f56c6c;
}

.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}

iframe {
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
