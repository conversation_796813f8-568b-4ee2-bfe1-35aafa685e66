<template>
  <el-dialog
    title="新建巡检"
    :visible.sync="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    :before-close="handleCancel"
    @close="handleCancel"
  >
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="选择设备：" prop="deviceIds">
        <el-tree
          ref="tree"
          :data="deviceData"
          show-checkbox
          node-key="id"
          :props="defaultProps"
          :default-expand-all="true"
          @check-change="handleTreeChange"
        ></el-tree>
      </el-form-item>
      
      <div class="content-tabs">
        <el-tabs v-model="inspectionType" type="card" @tab-click="changeType">
          <el-tab-pane label="手动巡检" name="1">
            <!-- 手动巡检无需额外配置 -->
          </el-tab-pane>
          <el-tab-pane label="自动周期巡检" name="2">
            <el-form-item label="定时开始时间：" prop="timePeriod">
              <el-select v-model="formData.timePeriod" placeholder="请选择">
                <el-option label="日" value="0"></el-option>
                <el-option label="周" value="1"></el-option>
                <el-option label="月" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="modalLoading" @click="handleSave">开始巡检</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addInspectionData, addInspectionSaveData } from '@/api/firewall/inspectionManagement'

export default {
  name: 'BatchInspection',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      inspectionType: '1',
      deviceData: [],
      deviceArr: [],
      modalLoading: false,
      formData: {
        deviceIds: [],
        timePeriod: '0',
      },
      defaultProps: {
        children: 'childList',
        label: 'groupName',
      },
      rules: {
        deviceIds: [
          { required: true, message: '请选择设备', trigger: 'change' }
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.getGroupList()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('on-cancel')
      }
    },
  },
  methods: {
    async getGroupList() {
      try {
        const res = await addInspectionData({})
        if (res.retcode === 0) {
          this.deviceData = res.data || []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
      }
    },
    handleTreeChange() {
      const checkedNodes = this.$refs.tree.getCheckedNodes()
      this.deviceArr = checkedNodes.map(node => node.id)
      this.formData.deviceIds = this.deviceArr
    },
    changeType(tab) {
      this.inspectionType = tab.name
    },
    handleCancel() {
      this.dialogVisible = false
      this.$emit('on-cancel')
    },
    handleSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.deviceArr.length === 0) {
            this.$message.error('请选择设备')
            return
          }
          
          this.modalLoading = true
          try {
            const payload = this.inspectionType === '2' ? {
              deviceIds: this.deviceArr.join(','),
              inspectionType: this.inspectionType,
              timePeriod: this.formData.timePeriod,
            } : {
              deviceIds: this.deviceArr.join(','),
              inspectionType: this.inspectionType,
            }
            
            const res = await addInspectionSaveData(payload)
            if (res.retcode === 0) {
              this.$message.success('添加成功!')
              this.handleCancel()
              this.$emit('on-submit')
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('保存失败')
          } finally {
            this.modalLoading = false
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.content-tabs {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
