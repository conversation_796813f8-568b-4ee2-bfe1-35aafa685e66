<template>
  <el-dialog
    title="查看巡检结果"
    :visible.sync="dialogVisible"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleCancel"
    @close="handleCancel"
  >
    <div v-loading="loading" class="public-block">
      <div class="public-block-details">
        <div class="table-bg">
          <el-table
            :data="recordList.rows || []"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
          >
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="liceneStatus" label="许可证状态" width="120"></el-table-column>
            <el-table-column prop="cpuStatus" label="CPU率" width="100"></el-table-column>
            <el-table-column prop="ramStatus" label="内存率" width="100"></el-table-column>
            <el-table-column prop="diskStatus" label="磁盘率" width="100"></el-table-column>
            <el-table-column prop="internetStatus" label="网络状态" width="120"></el-table-column>
          </el-table>
          
          <el-row type="flex" justify="end" style="margin-top: 10px;">
            <el-pagination
              :current-page="pageIndex"
              :page-size="pageSize"
              :total="recordList.total || 0"
              show-quick-jumper
              :show-total="showTotal"
              @current-change="handlePageChange"
              @size-change="onShowSizeChange"
            ></el-pagination>
          </el-row>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { inspectionResultPages } from '@/api/firewall/inspectionManagement'

export default {
  name: 'InspectionResult',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    recordData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      recordList: {},
      pageIndex: 1,
      pageSize: 10,
      loading: false,
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.recordData.id) {
        this.getInspectionData()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('on-cancel')
      }
    },
  },
  methods: {
    async getInspectionData() {
      this.loading = true
      try {
        const res = await inspectionResultPages({
          inspectionId: this.recordData.id,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
        })
        if (res.retcode === 0) {
          this.recordList = res.data || {}
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取巡检结果失败')
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.pageIndex = page
      this.getInspectionData()
    },
    onShowSizeChange(current, size) {
      this.pageSize = size
      this.pageIndex = 1
      this.getInspectionData()
    },
    showTotal(total) {
      return `总数据${total}条`
    },
    handleCancel() {
      this.dialogVisible = false
      this.$emit('on-cancel')
    },
  },
}
</script>

<style lang="scss" scoped>
.public-block {
  .public-block-details {
    .table-bg {
      background: #fff;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
