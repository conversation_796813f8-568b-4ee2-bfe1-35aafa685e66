<template>
  <el-dialog
    title="查看巡检详情"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleCancel"
    @close="handleCancel"
  >
    <div v-loading="loading" class="public-block">
      <div class="public-block-details">
        <div class="public-block-row">
          <span class="public-block-row-title">巡检类型：</span>
          <span>{{ recordList.inspectionType == 1 ? '手动' : '自动' }}</span>
        </div>
        <div class="public-block-row">
          <span class="public-block-row-title">巡检日期：</span>
          <span>{{ formatTime(recordList.inspectionDate) }}</span>
        </div>
        <div class="public-block-row">
          <span class="public-block-row-title">巡检数量：</span>
          <span>{{ recordList.totalCounts }}</span>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { inspectionInfo } from '@/api/firewall/inspectionManagement'
import dayjs from 'dayjs'

export default {
  name: 'ViewDetail',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    recordData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      recordList: {},
      loading: false,
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.recordData.id) {
        this.getInspectionData(this.recordData)
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('on-cancel')
      }
    },
  },
  methods: {
    async getInspectionData(record) {
      this.loading = true
      try {
        const res = await inspectionInfo({ id: record.id })
        if (res.retcode === 0) {
          this.recordList = res.data || {}
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取巡检详情失败')
      } finally {
        this.loading = false
      }
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleCancel() {
      this.dialogVisible = false
      this.$emit('on-cancel')
    },
  },
}
</script>

<style lang="scss" scoped>
.public-block {
  .public-block-details {
    .public-block-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .public-block-row-title {
        font-weight: bold;
        color: #333;
        min-width: 100px;
        margin-right: 8px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
