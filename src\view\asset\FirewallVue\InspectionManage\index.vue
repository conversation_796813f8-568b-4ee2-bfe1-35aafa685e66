<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.inspectionName" clearable placeholder="巡检名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="primary" @click="handleAdd">新建巡检任务</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.inspectionName" clearable placeholder="巡检名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.status" clearable placeholder="状态" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="运行中" value="running"></el-option>
                  <el-option label="已停止" value="stopped"></el-option>
                  <el-option label="已完成" value="completed"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">巡检任务管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="inspectionName" label="巡检名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="executeType" label="执行方式">
            <template slot-scope="scope">
              {{ getExecuteTypeText(scope.row.executeType) }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceCount" label="设备数量"></el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="getStatusClass(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="lastExecuteTime" label="最后执行时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.lastExecuteTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="nextExecuteTime" label="下次执行时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.nextExecuteTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button class="el-button--blue" type="text" @click="handleStart(scope.row)" v-if="scope.row.status === 'stopped'">启动</el-button>
                <el-button class="el-button--blue" type="text" @click="handleStop(scope.row)" v-if="scope.row.status === 'running'">停止</el-button>
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>

    <!-- 新增/编辑巡检任务对话框 -->
    <add-inspection-modal :visible.sync="addModalVisible" :current-data="currentData" @on-submit="handleAddSubmit"></add-inspection-modal>
  </div>
</template>

<script>
import { getInspectionList, deleteInspection, startInspection, stopInspection } from '@/api/firewall/inspectionManagement'
import AddInspectionModal from './components/AddInspectionModal.vue'
import dayjs from 'dayjs'

export default {
  name: 'InspectionManage',
  components: {
    AddInspectionModal,
  },
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        inspectionName: '',
        status: '',
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addModalVisible: false,
      currentData: null,
    }
  },
  mounted() {
    this.getInspectionList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getInspectionList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getInspectionList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取巡检任务列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.inspectionName) params.inspectionName = this.queryInput.inspectionName
      if (this.queryInput.status) params.status = this.queryInput.status
      return params
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getInspectionList()
    },
    handleReset() {
      this.queryInput = {
        inspectionName: '',
        status: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.currentData = null
      this.addModalVisible = true
    },
    handleEdit(record) {
      this.currentData = record
      this.addModalVisible = true
    },
    async handleStart(record) {
      try {
        const res = await startInspection({ id: record.id })
        if (res.retcode === 0) {
          this.$message.success('启动成功')
          this.getInspectionList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('启动失败')
      }
    },
    async handleStop(record) {
      try {
        const res = await stopInspection({ id: record.id })
        if (res.retcode === 0) {
          this.$message.success('停止成功')
          this.getInspectionList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('停止失败')
      }
    },
    handleDelete(record) {
      this.$confirm('确定要删除该巡检任务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteInspection({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getInspectionList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中巡检任务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            const res = await deleteInspection({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getInspectionList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleAddSubmit() {
      this.addModalVisible = false
      this.getInspectionList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getInspectionList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getInspectionList()
    },
    getExecuteTypeText(type) {
      const typeMap = {
        'immediate': '立即执行',
        'scheduled': '定时执行',
        'periodic': '周期执行',
      }
      return typeMap[type] || '-'
    },
    getStatusText(status) {
      const statusMap = {
        'running': '运行中',
        'stopped': '已停止',
        'completed': '已完成',
      }
      return statusMap[status] || '-'
    },
    getStatusClass(status) {
      const classMap = {
        'running': 'status-success',
        'stopped': 'status-failed',
        'completed': 'status-warning',
      }
      return classMap[status] || ''
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.status-warning {
  color: #e6a23c;
}
</style>
