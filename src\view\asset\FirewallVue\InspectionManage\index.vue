<template>
  <div>
    <el-card style="border-radius: 8px;" :bordered="false">
      <el-form class="searchBg" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
        <el-row>
          <el-col :span="8">
            <el-form-item label="起止日期">
              <el-date-picker
                v-model="queryInput.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="handleDateChange"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="searchBtn">
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button style="margin-left: 15px" @click="handleReset">清除</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <div style="margin-bottom: 20px; margin-top: 20px;">
      <el-button type="primary" style="margin-right: 8px" @click="handleAdd">新建巡检</el-button>
      <el-button style="border-radius: 2px" @click="handleBatchDelete">批量删除</el-button>
    </div>

    <el-card :bordered="false" style="border-radius: 8px;" class="TableContainer">
      <el-table
        :data="tableData"
        v-loading="loading"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        :row-selection="rowSelection"
        @selection-change="handleSelectionChange"
        :row-key="record => record.id"
      >
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="inspectionDate" label="巡检日期" width="100">
          <template slot-scope="scope">
            {{ formatTime(scope.row.inspectionDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="inspectionType" label="手动/自动" width="120">
          <template slot-scope="scope">
            {{ scope.row.inspectionType == 1 ? '手动' : '自动' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCounts" label="巡检设备数" width="80"></el-table-column>
        <el-table-column prop="inspectionStatus" label="巡检结果" width="120">
          <template slot-scope="scope">
            {{ getInspectionStatusText(scope.row.inspectionStatus) }}
          </template>
        </el-table-column>
        <el-table-column label="巡检状态" width="80">
          <template slot-scope="scope">
            <el-button
              type="text"
              class="el-button--blue"
              :disabled="scope.row.inspectionStatus !== 2"
              @click="handleExport(scope.row)"
            >
              导出
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="left">
          <template slot-scope="scope">
            <div style="color: #409eff;">
              <el-button
                type="text"
                class="el-button--blue"
                style="border-radius: 2px; margin-left: 10px;"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
              <el-button
                type="text"
                class="el-button--blue"
                style="border-radius: 2px; margin-left: 10px;"
                @click="handleLookDetail(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="text"
                class="el-button--blue"
                style="border-radius: 2px; margin-left: 10px;"
                @click="handleResult(scope.row)"
              >
                巡检结果
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-row type="flex" justify="end">
      <el-pagination
        :current-page="pagination.currentPage"
        show-quick-jumper
        show-size-changer
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :show-total="showTotal"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </el-row>

    <!-- 新增巡检对话框 -->
    <batch-inspection
      v-if="addModalVisible"
      :visible="addModalVisible"
      @on-submit="handleAddSubmit"
      @on-cancel="handleCancelClick"
    ></batch-inspection>

    <!-- 巡检结果对话框 -->
    <inspection-result
      v-if="resultModalVisible"
      :visible="resultModalVisible"
      :record-data="currentRecord"
      @on-cancel="handleCancelView"
    ></inspection-result>

    <!-- 查看详情对话框 -->
    <view-detail
      v-if="detailModalVisible"
      :visible="detailModalVisible"
      :record-data="currentRecord"
      @on-cancel="handleCancelView"
    ></view-detail>
  </div>
</template>

<script>
import { getInspectionList, deleteInspection, downloadExcel } from '@/api/firewall/inspectionManagement'
import BatchInspection from './components/BatchInspection.vue'
import InspectionResult from './components/InspectionResult.vue'
import ViewDetail from './components/ViewDetail.vue'
import dayjs from 'dayjs'

export default {
  name: 'InspectionManage',
  components: {
    BatchInspection,
    InspectionResult,
    ViewDetail,
  },
  data() {
    return {
      loading: false,
      queryInput: {
        dateRange: null,
      },
      tableData: [],
      selectedRows: [],
      notSelectedRowKeys: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
      },
      addModalVisible: false,
      resultModalVisible: false,
      detailModalVisible: false,
      currentRecord: null,
      rowSelection: {
        selectedRowKeys: [],
        onChange: this.onSelectChange,
        onSelectAll: this.onSelectAll,
        onSelect: this.onSelect,
      },
    }
  },
  mounted() {
    this.getInspectionList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getInspectionList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getInspectionList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.content || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取巡检任务列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.dateRange && this.queryInput.dateRange.length === 2) {
        params.startTime = this.queryInput.dateRange[0]
        params.endTime = this.queryInput.dateRange[1]
      }
      return params
    },
    handleDateChange(dateRange) {
      this.queryInput.dateRange = dateRange
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getInspectionList()
    },
    handleReset() {
      this.queryInput = {
        inspectionName: '',
        status: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.currentData = null
      this.addModalVisible = true
    },
    handleEdit(record) {
      this.currentData = record
      this.addModalVisible = true
    },
    async handleStart(record) {
      try {
        const res = await startInspection({ id: record.id })
        if (res.retcode === 0) {
          this.$message.success('启动成功')
          this.getInspectionList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('启动失败')
      }
    },
    async handleStop(record) {
      try {
        const res = await stopInspection({ id: record.id })
        if (res.retcode === 0) {
          this.$message.success('停止成功')
          this.getInspectionList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('停止失败')
      }
    },
    handleDelete(record) {
      this.$confirm('确定要删除该巡检任务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteInspection({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getInspectionList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中巡检任务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            const res = await deleteInspection({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getInspectionList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleAddSubmit() {
      this.addModalVisible = false
      this.getInspectionList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getInspectionList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getInspectionList()
    },
    getExecuteTypeText(type) {
      const typeMap = {
        'immediate': '立即执行',
        'scheduled': '定时执行',
        'periodic': '周期执行',
      }
      return typeMap[type] || '-'
    },
    getStatusText(status) {
      const statusMap = {
        'running': '运行中',
        'stopped': '已停止',
        'completed': '已完成',
      }
      return statusMap[status] || '-'
    },
    getStatusClass(status) {
      const classMap = {
        'running': 'status-success',
        'stopped': 'status-failed',
        'completed': 'status-warning',
      }
      return classMap[status] || ''
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.status-warning {
  color: #e6a23c;
}
</style>
