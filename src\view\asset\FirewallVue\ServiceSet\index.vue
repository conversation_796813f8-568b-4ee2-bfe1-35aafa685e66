<template>
  <div class="router-wrap-table">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="服务集管理" name="0">
        <header class="table-header">
          <section class="table-header-main">
            <section class="table-header-search">
              <section v-show="!isShow" class="table-header-search-input">
                <el-input v-model="queryInput.name" clearable placeholder="名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
              </section>
              <section class="table-header-search-button">
                <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="toggleShow">
                  高级搜索
                  <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
                </el-button>
              </section>
            </section>
            <section class="table-header-button">
              <el-button type="primary" @click="handleAdd">新建服务集</el-button>
              <el-button type="primary" @click="handleBatchIssue">批量下发</el-button>
              <el-button type="primary" @click="handleSyncProtocol">同步设备服务</el-button>
              <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
            </section>
          </section>
          <section class="table-header-extend">
            <el-collapse-transition>
              <div v-show="isShow">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-input v-model="queryInput.name" clearable placeholder="名称" @change="handleQuery"></el-input>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24" align="right">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                    <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-transition>
          </section>
        </header>

        <main class="table-body">
          <section class="table-body-header">
            <h2 class="table-body-title">服务集管理</h2>
          </section>
          <section v-loading="loading" class="table-body-main">
            <el-table
              :data="tableData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @selection-change="handleSelectionChange"
              :scroll="{ x: 1500 }"
            >
              <el-table-column type="selection" width="55" align="center"></el-table-column>
              <el-table-column label="序号" width="80" align="center">
                <template slot-scope="scope">
                  {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" show-overflow-tooltip width="200">
                <template slot-scope="scope">
                  <el-button type="text" class="el-button--blue" @click="handleView(scope.row)">
                    {{ scope.row.name }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="srcDeviceName" label="来源设备" show-overflow-tooltip></el-table-column>
              <el-table-column prop="srcIp" label="来源ip" width="100"></el-table-column>
              <el-table-column prop="protocol" label="协议类型" width="70"></el-table-column>
              <el-table-column prop="endPort" label="源端口高端口" width="100"></el-table-column>
              <el-table-column prop="port" label="源端口低端口" width="100"></el-table-column>
              <el-table-column prop="slEndPort" label="目的端口高端口" width="110"></el-table-column>
              <el-table-column prop="slPort" label="目的端口低端口" width="110"></el-table-column>
              <el-table-column prop="remark" label="备注" width="200"></el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <el-button class="el-button--blue" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)">删除</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleIssue(scope.row)">服务下发</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </main>
        <footer class="table-footer">
          <el-pagination
            v-if="pagination.visible"
            small
            background
            align="right"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          ></el-pagination>
        </footer>
      </el-tab-pane>

      <el-tab-pane label="服务集记录" name="1">
        <service-set-record />
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑服务集对话框 -->
    <add-service-modal :visible.sync="addModalVisible" :current-data="currentData" @on-submit="handleAddSubmit"></add-service-modal>

    <!-- 查看服务集对话框 -->
    <view-service-modal :visible.sync="viewModalVisible" :current-data="currentData"></view-service-modal>

    <!-- 设备选择组件 -->
    <device-component ref="deviceComponent" :operation-type="operationType" @on-submit="handleDeviceSubmit"></device-component>
  </div>
</template>

<script>
import { getServiceSetList, deleteServiceSet, issueServiceSet, syncServiceSetFromDevice } from '@/api/firewall/serviceSet'
import AddServiceModal from './components/AddServiceModal.vue'
import ViewServiceModal from './components/ViewServiceModal.vue'
import DeviceComponent from './components/DeviceComponent.vue'
import ServiceSetRecord from './components/ServiceSetRecord.vue'

export default {
  name: 'ServiceSet',
  components: {
    AddServiceModal,
    ViewServiceModal,
    DeviceComponent,
    ServiceSetRecord,
  },
  data() {
    return {
      activeTab: '0',
      isShow: false,
      loading: false,
      queryInput: {
        name: '',
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      addModalVisible: false,
      viewModalVisible: false,
      currentData: null,
      operationType: '', // '1': 下发, '2': 同步
    }
  },
  mounted() {
    this.getServiceSetList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getServiceSetList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getServiceSetList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取服务集列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.name) params.name = this.queryInput.name
      return params
    },
    handleTabClick(tab) {
      // 标签页切换逻辑
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getServiceSetList()
    },
    handleReset() {
      this.queryInput = {
        name: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.currentData = null
      this.addModalVisible = true
    },
    handleEdit(record) {
      this.currentData = record
      this.addModalVisible = true
    },
    handleView(record) {
      this.currentData = record
      this.viewModalVisible = true
    },
    handleDelete(record) {
      this.$confirm('确定要删除选中服务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteServiceSet({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getServiceSetList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中服务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map((row) => row.id).join(',')
            const res = await deleteServiceSet({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getServiceSetList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleIssue(record) {
      this.operationType = '1'
      this.$refs.deviceComponent.showDrawer(record, [record.id])
    },
    handleBatchIssue() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }
      this.operationType = '1'
      const ids = this.selectedRows.map((row) => row.id)
      this.$refs.deviceComponent.showDrawer({}, ids)
    },
    handleSyncProtocol() {
      this.operationType = '2'
      this.$refs.deviceComponent.showDrawer({}, [])
    },
    handleAddSubmit() {
      this.addModalVisible = false
      this.getServiceSetList()
    },
    handleDeviceSubmit() {
      this.getServiceSetList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getServiceSetList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getServiceSetList()
    },
  },
}
</script>

<style lang="scss" scoped>
.router-wrap-table {
  padding: 8px 24px;
  .table-header {
    background-color: transparent;
  }
}
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
