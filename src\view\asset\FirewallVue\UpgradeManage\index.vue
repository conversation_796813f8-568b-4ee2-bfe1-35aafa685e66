<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="queryInput.taskName" clearable placeholder="任务名称" prefix-icon="soc-icon-search" @change="handleQuery"></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="primary" @click="handleUpgrade">设备升级</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryInput.taskName" clearable placeholder="任务名称" @change="handleQuery"></el-input>
              </el-col>
              <el-col :span="6">
                <el-select v-model="queryInput.status" clearable placeholder="状态" @change="handleQuery">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="升级中" value="upgrading"></el-option>
                  <el-option label="成功" value="success"></el-option>
                  <el-option label="失败" value="failed"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">升级任务管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="upgradeFile" label="升级包" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deviceCount" label="设备数量"></el-table-column>
          <el-table-column prop="progress" label="进度">
            <template slot-scope="scope">
              <el-progress :percentage="scope.row.progress" :status="getProgressStatus(scope.row.status)"></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="getStatusClass(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleViewProgress(scope.row)" v-if="scope.row.status === 'upgrading'">查看进度</el-button>
                <el-button class="el-button--blue" type="text" @click="handleDelete(scope.row)" :disabled="scope.row.status === 'upgrading'">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>
    </footer>

    <!-- 设备升级对话框 -->
    <upgrade-modal :visible.sync="upgradeModalVisible" @on-submit="handleUpgradeSubmit"></upgrade-modal>

    <!-- 升级进度对话框 -->
    <progress-modal :visible.sync="progressModalVisible" :current-task="currentTask"></progress-modal>
  </div>
</template>

<script>
import { getUpgradeList, deleteUpgradePackage } from '@/api/firewall/upgradeManagement'
import UpgradeModal from './components/UpgradeModal.vue'
import ProgressModal from './components/ProgressModal.vue'
import dayjs from 'dayjs'

export default {
  name: 'UpgradeManage',
  components: {
    UpgradeModal,
    ProgressModal,
  },
  data() {
    return {
      isShow: false,
      loading: false,
      queryInput: {
        taskName: '',
        status: '',
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
        visible: true,
      },
      upgradeModalVisible: false,
      progressModalVisible: false,
      currentTask: {},
    }
  },
  mounted() {
    this.getUpgradeList()
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },
    async getUpgradeList() {
      this.loading = true
      const payload = {
        pageIndex: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.buildQueryParams(),
      }

      try {
        const res = await getUpgradeList(payload)
        if (res.retcode === 0) {
          this.tableData = res.data.rows || []
          this.pagination.total = res.data.total || 0
          this.selectedRows = []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('获取升级任务列表失败')
      } finally {
        this.loading = false
      }
    },
    buildQueryParams() {
      const params = {}
      if (this.queryInput.taskName) params.taskName = this.queryInput.taskName
      if (this.queryInput.status) params.status = this.queryInput.status
      return params
    },
    handleQuery() {
      this.pagination.currentPage = 1
      this.getUpgradeList()
    },
    handleReset() {
      this.queryInput = {
        taskName: '',
        status: '',
      }
      this.handleQuery()
    },
    handleUpgrade() {
      this.upgradeModalVisible = true
    },
    handleViewProgress(record) {
      this.currentTask = record
      this.progressModalVisible = true
    },
    handleDelete(record) {
      this.$confirm('确定要删除该升级任务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteUpgradePackage({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getUpgradeList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.error('至少选中一条数据')
        return
      }

      this.$confirm('确定要删除选中升级任务吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            const res = await deleteUpgradePackage({ ids })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getUpgradeList()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        })
        .catch(() => {})
    },
    handleUpgradeSubmit() {
      this.upgradeModalVisible = false
      this.getUpgradeList()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.getUpgradeList()
    },
    handlePageChange(page) {
      this.pagination.currentPage = page
      this.getUpgradeList()
    },
    getStatusText(status) {
      const statusMap = {
        'upgrading': '升级中',
        'success': '成功',
        'failed': '失败',
      }
      return statusMap[status] || '-'
    },
    getStatusClass(status) {
      const classMap = {
        'upgrading': 'status-warning',
        'success': 'status-success',
        'failed': 'status-failed',
      }
      return classMap[status] || ''
    },
    getProgressStatus(status) {
      if (status === 'success') return 'success'
      if (status === 'failed') return 'exception'
      return null
    },
    formatTime(time) {
      if (!time || time === '-') {
        return '-'
      }
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.status-warning {
  color: #e6a23c;
}
</style>
